_base_ = [
    '/22wangshenglong/FoodSeg/MambaVision/semantic_segmentation/configs/_base_/models/upernet_swin.py',
    '/22wangshenglong/FoodSeg/MambaVision/semantic_segmentation/configs/_base_/datasets/foodseg103.py',
    '/22wangshenglong/FoodSeg/MambaVision/semantic_segmentation/configs/_base_/default_runtime.py'
]

crop_size = (512, 512)
data_preprocessor = dict(size=crop_size)

# Model configuration for MambaVision Small
model = dict(
    data_preprocessor=data_preprocessor,
    backbone=dict(
        type='MM_mamba_vision',
        out_indices=(0, 1, 2, 3),
        pretrained=None,  # 将从ADE20K预训练权重开始训练
        depths=(3, 3, 7, 5),
        num_heads=(2, 4, 8, 16),
        window_size=(8, 8, 160, 56),
        dim=96,
        in_dim=64,
        mlp_ratio=4,
        drop_path_rate=0.7,
        norm_layer="ln2d",
        layer_scale=None
    ),
    decode_head=dict(in_channels=[96, 192, 384, 768], num_classes=104),
    auxiliary_head=dict(in_channels=384, num_classes=104))

# Training configuration
train_cfg = dict(type='IterBasedTrainLoop', max_iters=160000, val_interval=16000)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# Optimizer - AdamW for Small version
optimizer = dict(type='AdamW', lr=0.00005, betas=(0.9, 0.999), weight_decay=0.01)
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=optimizer,
    clip_grad=None,
    paramwise_cfg=dict(
        custom_keys={
            'pos_block': dict(decay_mult=0.),
            'norm': dict(decay_mult=0.),
            'head': dict(lr_mult=10.)
        }))

# Learning rate scheduler
param_scheduler = [
    dict(
        type='LinearLR', start_factor=1e-6, by_epoch=False, begin=0, end=1500),
    dict(
        type='PolyLR',
        eta_min=0.0,
        power=1.0,
        begin=1500,
        end=160000,
        by_epoch=False,
    )
]

# Data loader configuration - minimum batch size for BatchNorm
train_dataloader = dict(batch_size=2)  # Minimum 2 for BatchNorm to work
val_dataloader = dict(batch_size=1)
test_dataloader = val_dataloader

# Runtime settings
default_hooks = dict(
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=50, log_metric_by_epoch=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', by_epoch=False, interval=16000),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    visualization=dict(type='SegVisualizationHook'))

# Environment settings
env_cfg = dict(
    cudnn_benchmark=True,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

# Logging
log_processor = dict(by_epoch=False)
log_level = 'INFO'
load_from = '/22wangshenglong/FoodSeg/mamba_vision_160k_ade20k-512x512_small.pth'  # 从ADE20K预训练权重开始
resume = False
