2025/07/23 08:28:46 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:09:17) [GCC 11.2.0]
    CUDA available: True
    numpy_random_seed: 75678469
    GPU 0: NVIDIA A800 80GB PCIe
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 12.4, V12.4.131
    GCC: gcc (Ubuntu 9.3.0-17ubuntu1~20.04) 9.3.0
    PyTorch: 2.6.0+cu124
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201703
  - Intel(R) oneAPI Math Kernel Library Version 2023.1-Product Build 20230303 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.5.3 (Git Hash 66f0cb9eb66affd2da3bf5f8d897376f04aae6af)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX512
  - CUDA Runtime 12.4
  - NVCC architecture flags: -gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90
  - CuDNN 90.1
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=2236df1770800ffea5697b11b0bb0d910b2e59e1, CUDA_VERSION=12.4, CUDNN_VERSION=9.1.0, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -D_GLIBCXX_USE_CXX11_ABI=0 -fabi-version=11 -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -O2 -fPIC -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Werror=bool-operation -Wnarrowing -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wno-unused-parameter -Wno-strict-overflow -Wno-strict-aliasing -Wno-stringop-overflow -Wsuggest-override -Wno-psabi -Wno-error=old-style-cast -Wno-missing-braces -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.6.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=1, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=1, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, 

    TorchVision: 0.21.0+cu124
    OpenCV: 4.12.0
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 75678469
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/07/23 08:28:46 - mmengine - INFO - Config:
backbone_norm_cfg = dict(requires_grad=True, type='LN')
crop_size = (
    512,
    512,
)
data_preprocessor = dict(
    bgr_to_rgb=True,
    mean=[
        123.675,
        116.28,
        103.53,
    ],
    pad_val=0,
    seg_pad_val=255,
    size=(
        512,
        512,
    ),
    std=[
        58.395,
        57.12,
        57.375,
    ],
    type='SegDataPreProcessor')
data_root = '/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/Images'
dataset_type = 'ADE20KDataset'
default_hooks = dict(
    checkpoint=dict(by_epoch=False, interval=16000, type='CheckpointHook'),
    logger=dict(interval=50, log_metric_by_epoch=False, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='SegVisualizationHook'))
default_scope = 'mmseg'
env_cfg = dict(
    cudnn_benchmark=True,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
img_ratios = [
    0.5,
    0.75,
    1.0,
    1.25,
    1.5,
    1.75,
]
launcher = 'none'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=False)
model = dict(
    auxiliary_head=dict(
        align_corners=False,
        channels=256,
        concat_input=False,
        dropout_ratio=0.1,
        in_channels=320,
        in_index=2,
        loss_decode=dict(
            loss_weight=0.4, type='CrossEntropyLoss', use_sigmoid=False),
        norm_cfg=dict(requires_grad=True, type='SyncBN'),
        num_classes=104,
        num_convs=1,
        type='FCNHead'),
    backbone=dict(
        act_cfg=dict(type='GELU'),
        attn_drop_rate=0.0,
        depths=(
            1,
            3,
            8,
            4,
        ),
        dim=80,
        drop_path_rate=0.3,
        drop_rate=0.0,
        embed_dims=96,
        in_dim=32,
        layer_scale=None,
        mlp_ratio=4,
        norm_cfg=dict(requires_grad=True, type='LN'),
        norm_layer='ln2d',
        num_heads=(
            2,
            4,
            8,
            16,
        ),
        out_indices=(
            0,
            1,
            2,
            3,
        ),
        patch_norm=True,
        patch_size=4,
        pretrain_img_size=224,
        pretrained='/22wangshenglong/FoodSeg/mambavision_tiny_1k.pth.tar',
        qk_scale=None,
        qkv_bias=True,
        strides=(
            4,
            2,
            2,
            2,
        ),
        type='MM_mamba_vision',
        use_abs_pos_embed=False,
        window_size=(
            8,
            8,
            64,
            32,
        )),
    data_preprocessor=dict(
        bgr_to_rgb=True,
        mean=[
            123.675,
            116.28,
            103.53,
        ],
        pad_val=0,
        seg_pad_val=255,
        size=(
            512,
            512,
        ),
        std=[
            58.395,
            57.12,
            57.375,
        ],
        type='SegDataPreProcessor'),
    decode_head=dict(
        align_corners=False,
        channels=512,
        dropout_ratio=0.1,
        in_channels=[
            80,
            160,
            320,
            640,
        ],
        in_index=[
            0,
            1,
            2,
            3,
        ],
        loss_decode=dict(
            loss_weight=1.0, type='CrossEntropyLoss', use_sigmoid=False),
        norm_cfg=dict(requires_grad=True, type='SyncBN'),
        num_classes=104,
        pool_scales=(
            1,
            2,
            3,
            6,
        ),
        type='UPerHead'),
    pretrained=None,
    test_cfg=dict(mode='whole'),
    train_cfg=dict(),
    type='EncoderDecoder')
norm_cfg = dict(requires_grad=True, type='SyncBN')
num_classes = 104
optim_wrapper = dict(
    clip_grad=None,
    optimizer=dict(
        betas=(
            0.9,
            0.999,
        ),
        lr=6e-05,
        momentum=0.9,
        type='AdamW',
        weight_decay=0.01),
    paramwise_cfg=dict(
        custom_keys=dict(
            head=dict(lr_mult=10.0),
            norm=dict(decay_mult=0.0),
            pos_block=dict(decay_mult=0.0))),
    type='OptimWrapper')
optimizer = dict(
    betas=(
        0.9,
        0.999,
    ),
    lr=6e-05,
    momentum=0.9,
    type='AdamW',
    weight_decay=0.01)
param_scheduler = [
    dict(
        begin=0, by_epoch=False, end=1500, start_factor=1e-06,
        type='LinearLR'),
    dict(
        begin=1500,
        by_epoch=False,
        end=160000,
        eta_min=0.0,
        power=1.0,
        type='PolyLR'),
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=1,
    dataset=dict(
        data_prefix=dict(img_path='img_dir/test', seg_map_path='ann_dir/test'),
        data_root='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/Images',
        pipeline=[
            dict(type='LoadImageFromFile'),
            dict(keep_ratio=True, scale=(
                2048,
                512,
            ), type='Resize'),
            dict(reduce_zero_label=False, type='LoadAnnotations'),
            dict(type='PackSegInputs'),
        ],
        type='ADE20KDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    iou_metrics=[
        'mIoU',
    ], type='IoUMetric')
test_pipeline = [
    dict(type='LoadImageFromFile'),
    dict(keep_ratio=True, scale=(
        2048,
        512,
    ), type='Resize'),
    dict(reduce_zero_label=False, type='LoadAnnotations'),
    dict(type='PackSegInputs'),
]
train_cfg = dict(
    max_iters=160000, type='IterBasedTrainLoop', val_interval=16000)
train_dataloader = dict(
    batch_size=4,
    dataset=dict(
        data_prefix=dict(
            img_path='img_dir/train', seg_map_path='ann_dir/train'),
        data_root='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/Images',
        pipeline=[
            dict(type='LoadImageFromFile'),
            dict(reduce_zero_label=False, type='LoadAnnotations'),
            dict(
                keep_ratio=True,
                ratio_range=(
                    0.5,
                    2.0,
                ),
                scale=(
                    2048,
                    512,
                ),
                type='RandomResize'),
            dict(
                cat_max_ratio=0.75, crop_size=(
                    512,
                    512,
                ), type='RandomCrop'),
            dict(prob=0.5, type='RandomFlip'),
            dict(type='PhotoMetricDistortion'),
            dict(type='PackSegInputs'),
        ],
        type='ADE20KDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=True, type='InfiniteSampler'))
train_pipeline = [
    dict(type='LoadImageFromFile'),
    dict(reduce_zero_label=False, type='LoadAnnotations'),
    dict(
        keep_ratio=True,
        ratio_range=(
            0.5,
            2.0,
        ),
        scale=(
            2048,
            512,
        ),
        type='RandomResize'),
    dict(cat_max_ratio=0.75, crop_size=(
        512,
        512,
    ), type='RandomCrop'),
    dict(prob=0.5, type='RandomFlip'),
    dict(type='PhotoMetricDistortion'),
    dict(type='PackSegInputs'),
]
tta_model = dict(type='SegTTAModel')
tta_pipeline = [
    dict(backend_args=None, type='LoadImageFromFile'),
    dict(
        transforms=[
            [
                dict(keep_ratio=True, scale_factor=0.5, type='Resize'),
                dict(keep_ratio=True, scale_factor=0.75, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.0, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.25, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.5, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.75, type='Resize'),
            ],
            [
                dict(direction='horizontal', prob=0.0, type='RandomFlip'),
                dict(direction='horizontal', prob=1.0, type='RandomFlip'),
            ],
            [
                dict(reduce_zero_label=False, type='LoadAnnotations'),
            ],
            [
                dict(type='PackSegInputs'),
            ],
        ],
        type='TestTimeAug'),
]
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=1,
    dataset=dict(
        data_prefix=dict(img_path='img_dir/val', seg_map_path='ann_dir/val'),
        data_root='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/Images',
        pipeline=[
            dict(type='LoadImageFromFile'),
            dict(keep_ratio=True, scale=(
                2048,
                512,
            ), type='Resize'),
            dict(reduce_zero_label=False, type='LoadAnnotations'),
            dict(type='PackSegInputs'),
        ],
        type='ADE20KDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    iou_metrics=[
        'mIoU',
    ], type='IoUMetric')
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='SegLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = 'work_dirs/foodseg103_training'

2025/07/23 08:28:58 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/07/23 08:28:58 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SegVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SegVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SegVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.7.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm0.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm0.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm0.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm0.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm0.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm0.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm1.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm1.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm1.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm1.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm1.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm1.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm2.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm2.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm2.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm2.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm2.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm2.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm3.weight:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm3.weight:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm3.weight:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm3.bias:lr=6e-05
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm3.bias:weight_decay=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- backbone.outnorm3.bias:decay_mult=0.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.bias:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.conv.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.conv.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.conv.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.weight:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.weight:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.weight:lr_mult=10.0
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.bias:lr=0.0006000000000000001
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.bias:weight_decay=0.01
2025/07/23 08:28:58 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.bias:lr_mult=10.0
