2025/07/31 02:41:48 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:09:17) [GCC 11.2.0]
    CUDA available: True
    numpy_random_seed: 2105139517
    GPU 0: NVIDIA A800 80GB PCIe
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.4, V11.4.120
    GCC: gcc (Ubuntu 9.3.0-17ubuntu1~20.04) 9.3.0
    PyTorch: 2.6.0+cu124
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201703
  - Intel(R) oneAPI Math Kernel Library Version 2023.1-Product Build 20230303 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.5.3 (Git Hash 66f0cb9eb66affd2da3bf5f8d897376f04aae6af)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX512
  - CUDA Runtime 12.4
  - NVCC architecture flags: -gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90
  - CuDNN 90.1
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=2236df1770800ffea5697b11b0bb0d910b2e59e1, CUDA_VERSION=12.4, CUDNN_VERSION=9.1.0, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -D_GLIBCXX_USE_CXX11_ABI=0 -fabi-version=11 -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -O2 -fPIC -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Werror=bool-operation -Wnarrowing -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wno-unused-parameter -Wno-strict-overflow -Wno-strict-aliasing -Wno-stringop-overflow -Wsuggest-override -Wno-psabi -Wno-error=old-style-cast -Wno-missing-braces -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.6.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=1, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=1, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, 

    TorchVision: 0.21.0+cu124
    OpenCV: 4.12.0
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 2105139517
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/07/31 02:41:49 - mmengine - INFO - Config:
backbone_norm_cfg = dict(requires_grad=True, type='LN')
crop_size = (
    512,
    512,
)
data_preprocessor = dict(
    bgr_to_rgb=True,
    mean=[
        123.675,
        116.28,
        103.53,
    ],
    pad_val=0,
    seg_pad_val=255,
    size=(
        512,
        512,
    ),
    std=[
        58.395,
        57.12,
        57.375,
    ],
    type='SegDataPreProcessor')
data_root = '/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/Images'
dataset_type = 'ADE20KDataset'
default_hooks = dict(
    checkpoint=dict(by_epoch=False, interval=16000, type='CheckpointHook'),
    logger=dict(interval=50, log_metric_by_epoch=False, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='SegVisualizationHook'))
default_scope = 'mmseg'
env_cfg = dict(
    cudnn_benchmark=True,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
img_ratios = [
    0.5,
    0.75,
    1.0,
    1.25,
    1.5,
    1.75,
]
launcher = 'none'
load_from = 'work_dirs/foodseg103_training/iter_144000.pth'
log_level = 'INFO'
log_processor = dict(by_epoch=False)
model = dict(
    auxiliary_head=dict(
        align_corners=False,
        channels=256,
        concat_input=False,
        dropout_ratio=0.1,
        in_channels=320,
        in_index=2,
        loss_decode=dict(
            loss_weight=0.4, type='CrossEntropyLoss', use_sigmoid=False),
        norm_cfg=dict(requires_grad=True, type='SyncBN'),
        num_classes=104,
        num_convs=1,
        type='FCNHead'),
    backbone=dict(
        act_cfg=dict(type='GELU'),
        attn_drop_rate=0.0,
        depths=(
            1,
            3,
            8,
            4,
        ),
        dim=80,
        drop_path_rate=0.3,
        drop_rate=0.0,
        embed_dims=96,
        in_dim=32,
        layer_scale=None,
        mlp_ratio=4,
        norm_cfg=dict(requires_grad=True, type='LN'),
        norm_layer='ln2d',
        num_heads=(
            2,
            4,
            8,
            16,
        ),
        out_indices=(
            0,
            1,
            2,
            3,
        ),
        patch_norm=True,
        patch_size=4,
        pretrain_img_size=224,
        pretrained='/22wangshenglong/FoodSeg/mambavision_tiny_1k.pth.tar',
        qk_scale=None,
        qkv_bias=True,
        strides=(
            4,
            2,
            2,
            2,
        ),
        type='MM_mamba_vision',
        use_abs_pos_embed=False,
        window_size=(
            8,
            8,
            64,
            32,
        )),
    data_preprocessor=dict(
        bgr_to_rgb=True,
        mean=[
            123.675,
            116.28,
            103.53,
        ],
        pad_val=0,
        seg_pad_val=255,
        size=(
            512,
            512,
        ),
        std=[
            58.395,
            57.12,
            57.375,
        ],
        type='SegDataPreProcessor'),
    decode_head=dict(
        align_corners=False,
        channels=512,
        dropout_ratio=0.1,
        in_channels=[
            80,
            160,
            320,
            640,
        ],
        in_index=[
            0,
            1,
            2,
            3,
        ],
        loss_decode=dict(
            loss_weight=1.0, type='CrossEntropyLoss', use_sigmoid=False),
        norm_cfg=dict(requires_grad=True, type='SyncBN'),
        num_classes=104,
        pool_scales=(
            1,
            2,
            3,
            6,
        ),
        type='UPerHead'),
    pretrained=None,
    test_cfg=dict(mode='whole'),
    train_cfg=dict(),
    type='EncoderDecoder')
norm_cfg = dict(requires_grad=True, type='SyncBN')
num_classes = 104
optim_wrapper = dict(
    clip_grad=None,
    optimizer=dict(
        betas=(
            0.9,
            0.999,
        ), lr=6e-05, type='AdamW', weight_decay=0.01),
    paramwise_cfg=dict(
        custom_keys=dict(
            head=dict(lr_mult=10.0),
            norm=dict(decay_mult=0.0),
            pos_block=dict(decay_mult=0.0))),
    type='OptimWrapper')
optimizer = dict(
    betas=(
        0.9,
        0.999,
    ), lr=6e-05, type='AdamW', weight_decay=0.01)
param_scheduler = [
    dict(
        begin=0, by_epoch=False, end=1500, start_factor=1e-06,
        type='LinearLR'),
    dict(
        begin=1500,
        by_epoch=False,
        end=160000,
        eta_min=0.0,
        power=1.0,
        type='PolyLR'),
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=1,
    dataset=dict(
        data_prefix=dict(img_path='img_dir/test', seg_map_path='ann_dir/test'),
        data_root='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/test_folder',
        pipeline=[
            dict(type='LoadImageFromFile'),
            dict(keep_ratio=True, scale=(
                2048,
                512,
            ), type='Resize'),
            dict(reduce_zero_label=False, type='LoadAnnotations'),
            dict(type='PackSegInputs'),
        ],
        reduce_zero_label=False,
        type='ADE20KDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    iou_metrics=[
        'mIoU',
    ], type='IoUMetric')
test_pipeline = [
    dict(type='LoadImageFromFile'),
    dict(keep_ratio=True, scale=(
        2048,
        512,
    ), type='Resize'),
    dict(reduce_zero_label=False, type='LoadAnnotations'),
    dict(type='PackSegInputs'),
]
train_cfg = dict(
    max_iters=160000, type='IterBasedTrainLoop', val_interval=16000)
train_dataloader = dict(
    batch_size=4,
    dataset=dict(
        data_prefix=dict(
            img_path='img_dir/train', seg_map_path='ann_dir/train'),
        data_root='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/Images',
        pipeline=[
            dict(type='LoadImageFromFile'),
            dict(reduce_zero_label=False, type='LoadAnnotations'),
            dict(
                keep_ratio=True,
                ratio_range=(
                    0.5,
                    2.0,
                ),
                scale=(
                    2048,
                    512,
                ),
                type='RandomResize'),
            dict(
                cat_max_ratio=0.75, crop_size=(
                    512,
                    512,
                ), type='RandomCrop'),
            dict(prob=0.5, type='RandomFlip'),
            dict(type='PhotoMetricDistortion'),
            dict(type='PackSegInputs'),
        ],
        reduce_zero_label=False,
        type='ADE20KDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=True, type='InfiniteSampler'))
train_pipeline = [
    dict(type='LoadImageFromFile'),
    dict(reduce_zero_label=False, type='LoadAnnotations'),
    dict(
        keep_ratio=True,
        ratio_range=(
            0.5,
            2.0,
        ),
        scale=(
            2048,
            512,
        ),
        type='RandomResize'),
    dict(cat_max_ratio=0.75, crop_size=(
        512,
        512,
    ), type='RandomCrop'),
    dict(prob=0.5, type='RandomFlip'),
    dict(type='PhotoMetricDistortion'),
    dict(type='PackSegInputs'),
]
tta_model = dict(type='SegTTAModel')
tta_pipeline = [
    dict(backend_args=None, type='LoadImageFromFile'),
    dict(
        transforms=[
            [
                dict(keep_ratio=True, scale_factor=0.5, type='Resize'),
                dict(keep_ratio=True, scale_factor=0.75, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.0, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.25, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.5, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.75, type='Resize'),
            ],
            [
                dict(direction='horizontal', prob=0.0, type='RandomFlip'),
                dict(direction='horizontal', prob=1.0, type='RandomFlip'),
            ],
            [
                dict(reduce_zero_label=False, type='LoadAnnotations'),
            ],
            [
                dict(type='PackSegInputs'),
            ],
        ],
        type='TestTimeAug'),
]
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=1,
    dataset=dict(
        data_prefix=dict(img_path='img_dir/val', seg_map_path='ann_dir/val'),
        data_root='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/test_folder',
        pipeline=[
            dict(type='LoadImageFromFile'),
            dict(keep_ratio=True, scale=(
                2048,
                512,
            ), type='Resize'),
            dict(reduce_zero_label=False, type='LoadAnnotations'),
            dict(type='PackSegInputs'),
        ],
        reduce_zero_label=False,
        type='ADE20KDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    iou_metrics=[
        'mIoU',
    ], type='IoUMetric')
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='SegLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = 'work_dirs/test_results_144k'

2025/07/31 02:42:08 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/07/31 02:42:08 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SegVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SegVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SegVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2025/07/31 02:42:09 - mmengine - WARNING - The prefix is not set in metric class IoUMetric.
2025/07/31 02:42:09 - mmengine - INFO - Load checkpoint from work_dirs/foodseg103_training/iter_144000.pth
2025/07/31 02:42:38 - mmengine - INFO - per class results:
2025/07/31 02:42:38 - mmengine - INFO - 
+---------------------+-------+-------+
|        Class        |  IoU  |  Acc  |
+---------------------+-------+-------+
|         wall        | 95.12 |  99.1 |
|       building      |  0.0  |  nan  |
|         sky         |  nan  |  nan  |
|        floor        |  nan  |  nan  |
|         tree        |  0.0  |  0.0  |
|       ceiling       | 89.92 | 90.53 |
|         road        |  0.0  |  nan  |
|         bed         |  nan  |  nan  |
|      windowpane     |  0.0  |  nan  |
|        grass        | 66.38 | 74.87 |
|       cabinet       |  0.0  |  nan  |
|       sidewalk      | 90.29 |  97.8 |
|        person       |  nan  |  nan  |
|        earth        |  nan  |  nan  |
|         door        |  nan  |  nan  |
|        table        |  nan  |  nan  |
|       mountain      |  nan  |  nan  |
|        plant        |  nan  |  nan  |
|       curtain       |  nan  |  nan  |
|        chair        |  nan  |  nan  |
|         car         | 77.75 | 97.13 |
|        water        |  nan  |  nan  |
|       painting      |  nan  |  nan  |
|         sofa        |  nan  |  nan  |
|        shelf        |  nan  |  nan  |
|        house        |  3.91 |  5.37 |
|         sea         |  nan  |  nan  |
|        mirror       |  nan  |  nan  |
|         rug         |  nan  |  nan  |
|        field        | 43.11 | 45.39 |
|       armchair      | 56.28 | 61.04 |
|         seat        |  0.0  |  nan  |
|        fence        |  47.5 | 97.56 |
|         desk        |  0.0  |  nan  |
|         rock        |  nan  |  nan  |
|       wardrobe      |  0.0  |  0.0  |
|         lamp        |  nan  |  nan  |
|       bathtub       |  nan  |  nan  |
|       railing       |  nan  |  nan  |
|       cushion       |  nan  |  nan  |
|         base        | 94.74 | 96.41 |
|         box         | 38.77 | 41.55 |
|        column       | 79.25 | 79.45 |
|      signboard      |  0.0  |  nan  |
|   chest of drawers  |  nan  |  nan  |
|       counter       |  nan  |  nan  |
|         sand        | 13.16 | 94.95 |
|         sink        |  0.0  |  nan  |
|      skyscraper     | 88.42 | 89.39 |
|      fireplace      | 94.17 | 97.33 |
|     refrigerator    |  0.0  |  nan  |
|      grandstand     |  nan  |  nan  |
|         path        | 58.39 | 66.15 |
|        stairs       |  nan  |  nan  |
|        runway       | 85.11 | 85.17 |
|         case        |  nan  |  nan  |
|      pool table     | 58.79 | 59.28 |
|        pillow       |  nan  |  nan  |
|     screen door     | 56.56 |  56.8 |
|       stairway      | 76.18 | 98.98 |
|        river        |  nan  |  nan  |
|        bridge       |  nan  |  nan  |
|       bookcase      |  nan  |  nan  |
|        blind        |  nan  |  nan  |
|     coffee table    |  nan  |  nan  |
|        toilet       |  nan  |  nan  |
|        flower       | 82.16 | 96.63 |
|         book        | 93.93 | 97.77 |
|         hill        |  nan  |  nan  |
|        bench        |  nan  |  nan  |
|      countertop     | 40.19 | 65.93 |
|        stove        |  nan  |  nan  |
|         palm        |  0.0  |  nan  |
|    kitchen island   |  0.0  |  0.0  |
|       computer      |  nan  |  nan  |
|     swivel chair    |  nan  |  nan  |
|         boat        |  nan  |  nan  |
|         bar         |  nan  |  nan  |
|    arcade machine   |  nan  |  nan  |
|        hovel        |  nan  |  nan  |
|         bus         |  nan  |  nan  |
|        towel        |  nan  |  nan  |
|        light        |  10.0 | 10.21 |
|        truck        |  0.0  |  nan  |
|        tower        | 90.58 | 96.01 |
|      chandelier     |  nan  |  nan  |
|        awning       |  nan  |  nan  |
|     streetlight     | 87.64 | 99.13 |
|        booth        |  nan  |  nan  |
| television receiver | 52.19 | 56.54 |
|       airplane      |  0.0  |  nan  |
|      dirt track     |  0.0  |  nan  |
|       apparel       |  nan  |  nan  |
|         pole        | 33.04 | 96.28 |
|         land        |  0.0  |  nan  |
|      bannister      | 48.68 | 49.72 |
|      escalator      | 93.44 | 95.81 |
|       ottoman       |  nan  |  nan  |
|        bottle       |  0.0  |  0.0  |
|        buffet       |  nan  |  nan  |
|        poster       |  nan  |  nan  |
|        stage        |  nan  |  nan  |
|         van         |  nan  |  nan  |
|         ship        |  8.67 |  9.87 |
|       fountain      |  nan  |  nan  |
|    conveyer belt    |  nan  |  nan  |
|        canopy       |  nan  |  nan  |
|        washer       |  nan  |  nan  |
|      plaything      |  nan  |  nan  |
|    swimming pool    |  nan  |  nan  |
|        stool        |  nan  |  nan  |
|        barrel       |  nan  |  nan  |
|        basket       |  nan  |  nan  |
|      waterfall      |  nan  |  nan  |
|         tent        |  nan  |  nan  |
|         bag         |  nan  |  nan  |
|       minibike      |  nan  |  nan  |
|        cradle       |  nan  |  nan  |
|         oven        |  nan  |  nan  |
|         ball        |  nan  |  nan  |
|         food        |  nan  |  nan  |
|         step        |  nan  |  nan  |
|         tank        |  nan  |  nan  |
|      trade name     |  nan  |  nan  |
|      microwave      |  nan  |  nan  |
|         pot         |  nan  |  nan  |
|        animal       |  nan  |  nan  |
|       bicycle       |  nan  |  nan  |
|         lake        |  nan  |  nan  |
|      dishwasher     |  nan  |  nan  |
|        screen       |  nan  |  nan  |
|       blanket       |  nan  |  nan  |
|      sculpture      |  nan  |  nan  |
|         hood        |  nan  |  nan  |
|        sconce       |  nan  |  nan  |
|         vase        |  nan  |  nan  |
|    traffic light    |  nan  |  nan  |
|         tray        |  nan  |  nan  |
|        ashcan       |  nan  |  nan  |
|         fan         |  nan  |  nan  |
|         pier        |  nan  |  nan  |
|      crt screen     |  nan  |  nan  |
|        plate        |  nan  |  nan  |
|       monitor       |  nan  |  nan  |
|    bulletin board   |  nan  |  nan  |
|        shower       |  nan  |  nan  |
|       radiator      |  nan  |  nan  |
|        glass        |  nan  |  nan  |
|        clock        |  nan  |  nan  |
|         flag        |  nan  |  nan  |
+---------------------+-------+-------+
2025/07/31 02:42:38 - mmengine - INFO - Iter(test) [14/14]    aAcc: 85.8700  mIoU: 39.8800  mAcc: 65.9500  data_time: 0.0115  time: 2.0468
