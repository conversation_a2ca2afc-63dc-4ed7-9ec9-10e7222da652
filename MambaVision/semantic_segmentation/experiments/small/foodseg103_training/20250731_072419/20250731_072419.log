2025/07/31 07:24:20 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:09:17) [GCC 11.2.0]
    CUDA available: True
    numpy_random_seed: 838159540
    GPU 0: NVIDIA A800 80GB PCIe
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.4, V11.4.120
    GCC: gcc (Ubuntu 9.3.0-17ubuntu1~20.04) 9.3.0
    PyTorch: 2.6.0+cu124
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201703
  - Intel(R) oneAPI Math Kernel Library Version 2023.1-Product Build 20230303 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.5.3 (Git Hash 66f0cb9eb66affd2da3bf5f8d897376f04aae6af)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX512
  - CUDA Runtime 12.4
  - NVCC architecture flags: -gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90
  - CuDNN 90.1
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=2236df1770800ffea5697b11b0bb0d910b2e59e1, CUDA_VERSION=12.4, CUDNN_VERSION=9.1.0, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -D_GLIBCXX_USE_CXX11_ABI=0 -fabi-version=11 -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -O2 -fPIC -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Werror=bool-operation -Wnarrowing -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wno-unused-parameter -Wno-strict-overflow -Wno-strict-aliasing -Wno-stringop-overflow -Wsuggest-override -Wno-psabi -Wno-error=old-style-cast -Wno-missing-braces -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.6.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=1, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=1, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, 

    TorchVision: 0.21.0+cu124
    OpenCV: 4.12.0
    MMEngine: 0.10.1

Runtime environment:
    cudnn_benchmark: True
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 838159540
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/07/31 07:24:20 - mmengine - INFO - Config:
backbone_norm_cfg = dict(requires_grad=True, type='LN')
crop_size = (
    512,
    512,
)
data_preprocessor = dict(
    bgr_to_rgb=True,
    mean=[
        123.675,
        116.28,
        103.53,
    ],
    pad_val=0,
    seg_pad_val=255,
    size=(
        512,
        512,
    ),
    std=[
        58.395,
        57.12,
        57.375,
    ],
    type='SegDataPreProcessor')
data_root = '/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/Images'
dataset_type = 'ADE20KDataset'
default_hooks = dict(
    checkpoint=dict(by_epoch=False, interval=16000, type='CheckpointHook'),
    logger=dict(interval=50, log_metric_by_epoch=False, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    timer=dict(type='IterTimerHook'),
    visualization=dict(type='SegVisualizationHook'))
default_scope = 'mmseg'
env_cfg = dict(
    cudnn_benchmark=True,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
img_ratios = [
    0.5,
    0.75,
    1.0,
    1.25,
    1.5,
    1.75,
]
launcher = 'none'
load_from = '/22wangshenglong/FoodSeg/mamba_vision_160k_ade20k-512x512_small.pth'
log_level = 'INFO'
log_processor = dict(by_epoch=False)
model = dict(
    auxiliary_head=dict(
        align_corners=False,
        channels=256,
        concat_input=False,
        dropout_ratio=0.1,
        in_channels=384,
        in_index=2,
        loss_decode=dict(
            loss_weight=0.4, type='CrossEntropyLoss', use_sigmoid=False),
        norm_cfg=dict(requires_grad=True, type='SyncBN'),
        num_classes=104,
        num_convs=1,
        type='FCNHead'),
    backbone=dict(
        act_cfg=dict(type='GELU'),
        attn_drop_rate=0.0,
        depths=(
            3,
            3,
            7,
            5,
        ),
        dim=96,
        drop_path_rate=0.7,
        drop_rate=0.0,
        embed_dims=96,
        in_dim=64,
        layer_scale=None,
        mlp_ratio=4,
        norm_cfg=dict(requires_grad=True, type='LN'),
        norm_layer='ln2d',
        num_heads=(
            2,
            4,
            8,
            16,
        ),
        out_indices=(
            0,
            1,
            2,
            3,
        ),
        patch_norm=True,
        patch_size=4,
        pretrain_img_size=224,
        pretrained=None,
        qk_scale=None,
        qkv_bias=True,
        strides=(
            4,
            2,
            2,
            2,
        ),
        type='MM_mamba_vision',
        use_abs_pos_embed=False,
        window_size=(
            8,
            8,
            160,
            56,
        )),
    data_preprocessor=dict(
        bgr_to_rgb=True,
        mean=[
            123.675,
            116.28,
            103.53,
        ],
        pad_val=0,
        seg_pad_val=255,
        size=(
            512,
            512,
        ),
        std=[
            58.395,
            57.12,
            57.375,
        ],
        type='SegDataPreProcessor'),
    decode_head=dict(
        align_corners=False,
        channels=512,
        dropout_ratio=0.1,
        in_channels=[
            96,
            192,
            384,
            768,
        ],
        in_index=[
            0,
            1,
            2,
            3,
        ],
        loss_decode=dict(
            loss_weight=1.0, type='CrossEntropyLoss', use_sigmoid=False),
        norm_cfg=dict(requires_grad=True, type='SyncBN'),
        num_classes=104,
        pool_scales=(
            1,
            2,
            3,
            6,
        ),
        type='UPerHead'),
    pretrained=None,
    test_cfg=dict(mode='whole'),
    train_cfg=dict(),
    type='EncoderDecoder')
norm_cfg = dict(requires_grad=True, type='SyncBN')
num_classes = 104
optim_wrapper = dict(
    clip_grad=None,
    optimizer=dict(
        betas=(
            0.9,
            0.999,
        ), lr=5e-05, type='AdamW', weight_decay=0.01),
    paramwise_cfg=dict(
        custom_keys=dict(
            head=dict(lr_mult=10.0),
            norm=dict(decay_mult=0.0),
            pos_block=dict(decay_mult=0.0))),
    type='OptimWrapper')
optimizer = dict(
    betas=(
        0.9,
        0.999,
    ), lr=5e-05, type='AdamW', weight_decay=0.01)
param_scheduler = [
    dict(
        begin=0, by_epoch=False, end=1500, start_factor=1e-06,
        type='LinearLR'),
    dict(
        begin=1500,
        by_epoch=False,
        end=160000,
        eta_min=0.0,
        power=1.0,
        type='PolyLR'),
]
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=1,
    dataset=dict(
        data_prefix=dict(img_path='img_dir/test', seg_map_path='ann_dir/test'),
        data_root='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/test_folder',
        pipeline=[
            dict(type='LoadImageFromFile'),
            dict(keep_ratio=True, scale=(
                2048,
                512,
            ), type='Resize'),
            dict(reduce_zero_label=False, type='LoadAnnotations'),
            dict(type='PackSegInputs'),
        ],
        reduce_zero_label=False,
        type='ADE20KDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = dict(
    iou_metrics=[
        'mIoU',
    ], type='IoUMetric')
test_pipeline = [
    dict(type='LoadImageFromFile'),
    dict(keep_ratio=True, scale=(
        2048,
        512,
    ), type='Resize'),
    dict(reduce_zero_label=False, type='LoadAnnotations'),
    dict(type='PackSegInputs'),
]
train_cfg = dict(
    max_iters=160000, type='IterBasedTrainLoop', val_interval=16000)
train_dataloader = dict(
    batch_size=2,
    dataset=dict(
        data_prefix=dict(
            img_path='img_dir/train', seg_map_path='ann_dir/train'),
        data_root='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/Images',
        pipeline=[
            dict(type='LoadImageFromFile'),
            dict(reduce_zero_label=False, type='LoadAnnotations'),
            dict(
                keep_ratio=True,
                ratio_range=(
                    0.5,
                    2.0,
                ),
                scale=(
                    2048,
                    512,
                ),
                type='RandomResize'),
            dict(
                cat_max_ratio=0.75, crop_size=(
                    512,
                    512,
                ), type='RandomCrop'),
            dict(prob=0.5, type='RandomFlip'),
            dict(type='PhotoMetricDistortion'),
            dict(type='PackSegInputs'),
        ],
        reduce_zero_label=False,
        type='ADE20KDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=True, type='InfiniteSampler'))
train_pipeline = [
    dict(type='LoadImageFromFile'),
    dict(reduce_zero_label=False, type='LoadAnnotations'),
    dict(
        keep_ratio=True,
        ratio_range=(
            0.5,
            2.0,
        ),
        scale=(
            2048,
            512,
        ),
        type='RandomResize'),
    dict(cat_max_ratio=0.75, crop_size=(
        512,
        512,
    ), type='RandomCrop'),
    dict(prob=0.5, type='RandomFlip'),
    dict(type='PhotoMetricDistortion'),
    dict(type='PackSegInputs'),
]
tta_model = dict(type='SegTTAModel')
tta_pipeline = [
    dict(backend_args=None, type='LoadImageFromFile'),
    dict(
        transforms=[
            [
                dict(keep_ratio=True, scale_factor=0.5, type='Resize'),
                dict(keep_ratio=True, scale_factor=0.75, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.0, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.25, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.5, type='Resize'),
                dict(keep_ratio=True, scale_factor=1.75, type='Resize'),
            ],
            [
                dict(direction='horizontal', prob=0.0, type='RandomFlip'),
                dict(direction='horizontal', prob=1.0, type='RandomFlip'),
            ],
            [
                dict(reduce_zero_label=False, type='LoadAnnotations'),
            ],
            [
                dict(type='PackSegInputs'),
            ],
        ],
        type='TestTimeAug'),
]
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=1,
    dataset=dict(
        data_prefix=dict(img_path='img_dir/val', seg_map_path='ann_dir/val'),
        data_root='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/test_folder',
        pipeline=[
            dict(type='LoadImageFromFile'),
            dict(keep_ratio=True, scale=(
                2048,
                512,
            ), type='Resize'),
            dict(reduce_zero_label=False, type='LoadAnnotations'),
            dict(type='PackSegInputs'),
        ],
        reduce_zero_label=False,
        type='ADE20KDataset'),
    num_workers=4,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = dict(
    iou_metrics=[
        'mIoU',
    ], type='IoUMetric')
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    name='visualizer',
    type='SegLocalVisualizer',
    vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = 'experiments/small/foodseg103_training'

2025/07/31 07:24:38 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/07/31 07:24:38 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SegVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SegVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SegVisualizationHook               
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.0.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.1.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.0.blocks.2.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.0.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.1.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.1.blocks.2.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.0.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.1.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.2.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.3.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.4.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.5.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.2.blocks.6.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.0.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.1.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.2.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.3.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.levels.3.blocks.4.norm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm0.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm0.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm0.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm0.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm0.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm0.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm1.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm1.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm1.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm1.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm1.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm1.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm2.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm2.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm2.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm2.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm2.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm2.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm3.weight:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm3.weight:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm3.weight:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm3.bias:lr=5e-05
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm3.bias:weight_decay=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- backbone.outnorm3.bias:decay_mult=0.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.conv_seg.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.0.1.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.1.1.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.2.1.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.psp_modules.3.1.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.bottleneck.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.0.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.1.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.lateral_convs.2.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.0.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.1.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_convs.2.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- decode_head.fpn_bottleneck.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.conv_seg.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.conv.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.conv.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.conv.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.weight:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.weight:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.weight:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.bias:lr=0.0005
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.bias:weight_decay=0.01
2025/07/31 07:24:39 - mmengine - INFO - paramwise_options -- auxiliary_head.convs.0.bn.bias:lr_mult=10.0
2025/07/31 07:24:39 - mmengine - WARNING - The prefix is not set in metric class IoUMetric.
Name of parameter - Initialization information

backbone.patch_embed.conv_down.0.weight - torch.Size([64, 3, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.patch_embed.conv_down.1.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.patch_embed.conv_down.1.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.patch_embed.conv_down.3.weight - torch.Size([96, 64, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.patch_embed.conv_down.4.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.patch_embed.conv_down.4.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.0.conv1.weight - torch.Size([96, 96, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.0.conv1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.0.norm1.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.0.norm1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.0.conv2.weight - torch.Size([96, 96, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.0.conv2.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.0.norm2.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.0.norm2.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.1.conv1.weight - torch.Size([96, 96, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.1.conv1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.1.norm1.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.1.norm1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.1.conv2.weight - torch.Size([96, 96, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.1.conv2.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.1.norm2.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.1.norm2.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.2.conv1.weight - torch.Size([96, 96, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.2.conv1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.2.norm1.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.2.norm1.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.2.conv2.weight - torch.Size([96, 96, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.2.conv2.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.2.norm2.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.blocks.2.norm2.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.0.downsample.reduction.0.weight - torch.Size([192, 96, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.0.conv1.weight - torch.Size([192, 192, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.0.conv1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.0.norm1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.0.norm1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.0.conv2.weight - torch.Size([192, 192, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.0.conv2.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.0.norm2.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.0.norm2.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.1.conv1.weight - torch.Size([192, 192, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.1.conv1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.1.norm1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.1.norm1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.1.conv2.weight - torch.Size([192, 192, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.1.conv2.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.1.norm2.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.1.norm2.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.2.conv1.weight - torch.Size([192, 192, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.2.conv1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.2.norm1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.2.norm1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.2.conv2.weight - torch.Size([192, 192, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.2.conv2.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.2.norm2.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.blocks.2.norm2.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.1.downsample.reduction.0.weight - torch.Size([384, 192, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.norm1.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.norm1.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mixer.A_log - torch.Size([192, 8]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mixer.D - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mixer.in_proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mixer.x_proj.weight - torch.Size([40, 192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mixer.dt_proj.weight - torch.Size([192, 24]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mixer.dt_proj.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mixer.out_proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mixer.conv1d_x.weight - torch.Size([192, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mixer.conv1d_z.weight - torch.Size([192, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.norm2.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.norm2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mlp.fc1.weight - torch.Size([1536, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mlp.fc1.bias - torch.Size([1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mlp.fc2.weight - torch.Size([384, 1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.0.mlp.fc2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.norm1.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.norm1.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mixer.A_log - torch.Size([192, 8]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mixer.D - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mixer.in_proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mixer.x_proj.weight - torch.Size([40, 192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mixer.dt_proj.weight - torch.Size([192, 24]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mixer.dt_proj.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mixer.out_proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mixer.conv1d_x.weight - torch.Size([192, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mixer.conv1d_z.weight - torch.Size([192, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.norm2.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.norm2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mlp.fc1.weight - torch.Size([1536, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mlp.fc1.bias - torch.Size([1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mlp.fc2.weight - torch.Size([384, 1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.1.mlp.fc2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.norm1.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.norm1.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mixer.A_log - torch.Size([192, 8]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mixer.D - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mixer.in_proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mixer.x_proj.weight - torch.Size([40, 192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mixer.dt_proj.weight - torch.Size([192, 24]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mixer.dt_proj.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mixer.out_proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mixer.conv1d_x.weight - torch.Size([192, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mixer.conv1d_z.weight - torch.Size([192, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.norm2.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.norm2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mlp.fc1.weight - torch.Size([1536, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mlp.fc1.bias - torch.Size([1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mlp.fc2.weight - torch.Size([384, 1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.2.mlp.fc2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.norm1.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.norm1.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mixer.A_log - torch.Size([192, 8]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mixer.D - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mixer.in_proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mixer.x_proj.weight - torch.Size([40, 192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mixer.dt_proj.weight - torch.Size([192, 24]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mixer.dt_proj.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mixer.out_proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mixer.conv1d_x.weight - torch.Size([192, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mixer.conv1d_z.weight - torch.Size([192, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.norm2.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.norm2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mlp.fc1.weight - torch.Size([1536, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mlp.fc1.bias - torch.Size([1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mlp.fc2.weight - torch.Size([384, 1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.3.mlp.fc2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.norm1.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.norm1.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.mixer.qkv.weight - torch.Size([1152, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.mixer.qkv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.mixer.proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.mixer.proj.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.norm2.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.norm2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.mlp.fc1.weight - torch.Size([1536, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.mlp.fc1.bias - torch.Size([1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.mlp.fc2.weight - torch.Size([384, 1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.4.mlp.fc2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.norm1.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.norm1.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.mixer.qkv.weight - torch.Size([1152, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.mixer.qkv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.mixer.proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.mixer.proj.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.norm2.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.norm2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.mlp.fc1.weight - torch.Size([1536, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.mlp.fc1.bias - torch.Size([1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.mlp.fc2.weight - torch.Size([384, 1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.5.mlp.fc2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.norm1.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.norm1.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.mixer.qkv.weight - torch.Size([1152, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.mixer.qkv.bias - torch.Size([1152]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.mixer.proj.weight - torch.Size([384, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.mixer.proj.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.norm2.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.norm2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.mlp.fc1.weight - torch.Size([1536, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.mlp.fc1.bias - torch.Size([1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.mlp.fc2.weight - torch.Size([384, 1536]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.blocks.6.mlp.fc2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.2.downsample.reduction.0.weight - torch.Size([768, 384, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.norm1.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.norm1.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mixer.A_log - torch.Size([384, 8]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mixer.D - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mixer.in_proj.weight - torch.Size([768, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mixer.x_proj.weight - torch.Size([64, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mixer.dt_proj.weight - torch.Size([384, 48]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mixer.dt_proj.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mixer.out_proj.weight - torch.Size([768, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mixer.conv1d_x.weight - torch.Size([384, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mixer.conv1d_z.weight - torch.Size([384, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.norm2.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.norm2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mlp.fc1.weight - torch.Size([3072, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mlp.fc1.bias - torch.Size([3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mlp.fc2.weight - torch.Size([768, 3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.0.mlp.fc2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.norm1.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.norm1.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mixer.A_log - torch.Size([384, 8]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mixer.D - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mixer.in_proj.weight - torch.Size([768, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mixer.x_proj.weight - torch.Size([64, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mixer.dt_proj.weight - torch.Size([384, 48]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mixer.dt_proj.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mixer.out_proj.weight - torch.Size([768, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mixer.conv1d_x.weight - torch.Size([384, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mixer.conv1d_z.weight - torch.Size([384, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.norm2.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.norm2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mlp.fc1.weight - torch.Size([3072, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mlp.fc1.bias - torch.Size([3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mlp.fc2.weight - torch.Size([768, 3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.1.mlp.fc2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.norm1.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.norm1.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mixer.A_log - torch.Size([384, 8]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mixer.D - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mixer.in_proj.weight - torch.Size([768, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mixer.x_proj.weight - torch.Size([64, 384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mixer.dt_proj.weight - torch.Size([384, 48]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mixer.dt_proj.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mixer.out_proj.weight - torch.Size([768, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mixer.conv1d_x.weight - torch.Size([384, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mixer.conv1d_z.weight - torch.Size([384, 1, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.norm2.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.norm2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mlp.fc1.weight - torch.Size([3072, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mlp.fc1.bias - torch.Size([3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mlp.fc2.weight - torch.Size([768, 3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.2.mlp.fc2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.norm1.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.norm1.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.mixer.qkv.weight - torch.Size([2304, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.mixer.qkv.bias - torch.Size([2304]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.mixer.proj.weight - torch.Size([768, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.mixer.proj.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.norm2.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.norm2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.mlp.fc1.weight - torch.Size([3072, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.mlp.fc1.bias - torch.Size([3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.mlp.fc2.weight - torch.Size([768, 3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.3.mlp.fc2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.norm1.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.norm1.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.mixer.qkv.weight - torch.Size([2304, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.mixer.qkv.bias - torch.Size([2304]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.mixer.proj.weight - torch.Size([768, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.mixer.proj.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.norm2.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.norm2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.mlp.fc1.weight - torch.Size([3072, 768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.mlp.fc1.bias - torch.Size([3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.mlp.fc2.weight - torch.Size([768, 3072]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.levels.3.blocks.4.mlp.fc2.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.outnorm0.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.outnorm0.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.outnorm1.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.outnorm1.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.outnorm2.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.outnorm2.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.outnorm3.weight - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

backbone.outnorm3.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.conv_seg.weight - torch.Size([104, 512, 1, 1]): 
NormalInit: mean=0, std=0.01, bias=0 

decode_head.conv_seg.bias - torch.Size([104]): 
NormalInit: mean=0, std=0.01, bias=0 

decode_head.psp_modules.0.1.conv.weight - torch.Size([512, 768, 1, 1]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.0.1.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.0.1.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.1.1.conv.weight - torch.Size([512, 768, 1, 1]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.1.1.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.1.1.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.2.1.conv.weight - torch.Size([512, 768, 1, 1]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.2.1.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.2.1.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.3.1.conv.weight - torch.Size([512, 768, 1, 1]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.3.1.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.psp_modules.3.1.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.bottleneck.conv.weight - torch.Size([512, 2816, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

decode_head.bottleneck.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.bottleneck.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.lateral_convs.0.conv.weight - torch.Size([512, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.lateral_convs.0.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.lateral_convs.0.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.lateral_convs.1.conv.weight - torch.Size([512, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.lateral_convs.1.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.lateral_convs.1.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.lateral_convs.2.conv.weight - torch.Size([512, 384, 1, 1]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.lateral_convs.2.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.lateral_convs.2.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_convs.0.conv.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_convs.0.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_convs.0.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_convs.1.conv.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_convs.1.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_convs.1.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_convs.2.conv.weight - torch.Size([512, 512, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_convs.2.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_convs.2.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_bottleneck.conv.weight - torch.Size([512, 2048, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

decode_head.fpn_bottleneck.bn.weight - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

decode_head.fpn_bottleneck.bn.bias - torch.Size([512]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

auxiliary_head.conv_seg.weight - torch.Size([104, 256, 1, 1]): 
NormalInit: mean=0, std=0.01, bias=0 

auxiliary_head.conv_seg.bias - torch.Size([104]): 
NormalInit: mean=0, std=0.01, bias=0 

auxiliary_head.convs.0.conv.weight - torch.Size([256, 384, 3, 3]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

auxiliary_head.convs.0.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  

auxiliary_head.convs.0.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of EncoderDecoder  
2025/07/31 07:24:40 - mmengine - INFO - Load checkpoint from /22wangshenglong/FoodSeg/mamba_vision_160k_ade20k-512x512_small.pth
2025/07/31 07:24:40 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/07/31 07:24:40 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/07/31 07:24:40 - mmengine - INFO - Checkpoints will be saved to /22wangshenglong/FoodSeg/MambaVision/semantic_segmentation/experiments/small/foodseg103_training.
2025/07/31 07:26:18 - mmengine - INFO - Iter(train) [    50/160000]  base_lr: 1.6345e-06 lr: 1.6345e-06  eta: 3 days, 14:48:57  time: 1.8520  data_time: 0.0053  memory: 44938  loss: 5.2052  decode.loss_ce: 3.7005  decode.acc_seg: 6.6757  aux.loss_ce: 1.5047  aux.acc_seg: 2.5739
2025/07/31 07:27:51 - mmengine - INFO - Iter(train) [   100/160000]  base_lr: 3.3022e-06 lr: 3.3022e-06  eta: 3 days, 12:50:38  time: 1.8733  data_time: 0.0052  memory: 13266  loss: 5.5062  decode.loss_ce: 3.8267  decode.acc_seg: 35.7042  aux.loss_ce: 1.6796  aux.acc_seg: 29.7878
2025/07/31 07:29:25 - mmengine - INFO - Iter(train) [   150/160000]  base_lr: 4.9700e-06 lr: 4.9700e-06  eta: 3 days, 12:16:12  time: 1.8786  data_time: 0.0052  memory: 13266  loss: 4.2815  decode.loss_ce: 2.9039  decode.acc_seg: 31.9752  aux.loss_ce: 1.3776  aux.acc_seg: 33.4368
2025/07/31 07:30:59 - mmengine - INFO - Iter(train) [   200/160000]  base_lr: 6.6378e-06 lr: 6.6378e-06  eta: 3 days, 12:01:57  time: 1.8902  data_time: 0.0054  memory: 13266  loss: 3.2031  decode.loss_ce: 2.1128  decode.acc_seg: 52.5604  aux.loss_ce: 1.0903  aux.acc_seg: 36.8774
2025/07/31 07:32:33 - mmengine - INFO - Iter(train) [   250/160000]  base_lr: 8.3056e-06 lr: 8.3056e-06  eta: 3 days, 11:50:58  time: 1.8898  data_time: 0.0054  memory: 13266  loss: 3.9935  decode.loss_ce: 2.7220  decode.acc_seg: 36.7145  aux.loss_ce: 1.2715  aux.acc_seg: 33.7389
2025/07/31 07:34:06 - mmengine - INFO - Iter(train) [   300/160000]  base_lr: 9.9734e-06 lr: 9.9734e-06  eta: 3 days, 11:41:32  time: 1.8810  data_time: 0.0051  memory: 13266  loss: 3.4064  decode.loss_ce: 2.3459  decode.acc_seg: 24.5361  aux.loss_ce: 1.0605  aux.acc_seg: 21.5017
2025/07/31 07:35:40 - mmengine - INFO - Iter(train) [   350/160000]  base_lr: 1.1641e-05 lr: 1.1641e-05  eta: 3 days, 11:37:17  time: 1.8749  data_time: 0.0050  memory: 13266  loss: 3.2420  decode.loss_ce: 2.1909  decode.acc_seg: 41.3956  aux.loss_ce: 1.0511  aux.acc_seg: 31.5418
2025/07/31 07:37:14 - mmengine - INFO - Iter(train) [   400/160000]  base_lr: 1.3309e-05 lr: 1.3309e-05  eta: 3 days, 11:32:58  time: 1.8801  data_time: 0.0049  memory: 13266  loss: 3.4985  decode.loss_ce: 2.4071  decode.acc_seg: 37.5397  aux.loss_ce: 1.0914  aux.acc_seg: 39.6727
2025/07/31 07:38:48 - mmengine - INFO - Iter(train) [   450/160000]  base_lr: 1.4977e-05 lr: 1.4977e-05  eta: 3 days, 11:29:07  time: 1.8765  data_time: 0.0051  memory: 13266  loss: 3.1505  decode.loss_ce: 2.1185  decode.acc_seg: 28.5926  aux.loss_ce: 1.0320  aux.acc_seg: 11.1441
2025/07/31 07:40:22 - mmengine - INFO - Iter(train) [   500/160000]  base_lr: 1.6644e-05 lr: 1.6644e-05  eta: 3 days, 11:26:34  time: 1.8772  data_time: 0.0051  memory: 13266  loss: 3.2937  decode.loss_ce: 2.2643  decode.acc_seg: 37.9255  aux.loss_ce: 1.0294  aux.acc_seg: 26.4753
2025/07/31 07:41:56 - mmengine - INFO - Iter(train) [   550/160000]  base_lr: 1.8312e-05 lr: 1.8312e-05  eta: 3 days, 11:22:56  time: 1.8665  data_time: 0.0052  memory: 13266  loss: 2.8792  decode.loss_ce: 1.9808  decode.acc_seg: 45.1408  aux.loss_ce: 0.8984  aux.acc_seg: 32.1716
2025/07/31 07:43:29 - mmengine - INFO - Iter(train) [   600/160000]  base_lr: 1.9980e-05 lr: 1.9980e-05  eta: 3 days, 11:19:15  time: 1.8817  data_time: 0.0051  memory: 13266  loss: 3.1377  decode.loss_ce: 2.1717  decode.acc_seg: 48.2576  aux.loss_ce: 0.9660  aux.acc_seg: 42.3348
2025/07/31 07:45:03 - mmengine - INFO - Iter(train) [   650/160000]  base_lr: 2.1648e-05 lr: 2.1648e-05  eta: 3 days, 11:17:04  time: 1.8802  data_time: 0.0051  memory: 13266  loss: 2.8503  decode.loss_ce: 1.9635  decode.acc_seg: 46.0768  aux.loss_ce: 0.8868  aux.acc_seg: 45.3072
2025/07/31 07:46:37 - mmengine - INFO - Iter(train) [   700/160000]  base_lr: 2.3316e-05 lr: 2.3316e-05  eta: 3 days, 11:14:13  time: 1.8825  data_time: 0.0052  memory: 13266  loss: 2.5759  decode.loss_ce: 1.7709  decode.acc_seg: 25.4309  aux.loss_ce: 0.8050  aux.acc_seg: 19.6615
2025/07/31 07:48:10 - mmengine - INFO - Iter(train) [   750/160000]  base_lr: 2.4983e-05 lr: 2.4983e-05  eta: 3 days, 11:10:36  time: 1.8812  data_time: 0.0051  memory: 13266  loss: 3.1504  decode.loss_ce: 2.2121  decode.acc_seg: 45.7176  aux.loss_ce: 0.9383  aux.acc_seg: 47.3823
2025/07/31 07:49:44 - mmengine - INFO - Iter(train) [   800/160000]  base_lr: 2.6651e-05 lr: 2.6651e-05  eta: 3 days, 11:07:22  time: 1.8775  data_time: 0.0053  memory: 13266  loss: 2.9124  decode.loss_ce: 2.0388  decode.acc_seg: 57.8782  aux.loss_ce: 0.8735  aux.acc_seg: 53.2561
2025/07/31 07:51:18 - mmengine - INFO - Iter(train) [   850/160000]  base_lr: 2.8319e-05 lr: 2.8319e-05  eta: 3 days, 11:04:48  time: 1.8700  data_time: 0.0051  memory: 13266  loss: 2.6738  decode.loss_ce: 1.8294  decode.acc_seg: 41.7358  aux.loss_ce: 0.8444  aux.acc_seg: 57.1727
2025/07/31 07:52:51 - mmengine - INFO - Iter(train) [   900/160000]  base_lr: 2.9987e-05 lr: 2.9987e-05  eta: 3 days, 11:01:16  time: 1.8771  data_time: 0.0055  memory: 13266  loss: 2.9758  decode.loss_ce: 2.0894  decode.acc_seg: 52.8885  aux.loss_ce: 0.8864  aux.acc_seg: 43.9886
2025/07/31 07:54:25 - mmengine - INFO - Iter(train) [   950/160000]  base_lr: 3.1654e-05 lr: 3.1654e-05  eta: 3 days, 10:59:15  time: 1.8780  data_time: 0.0048  memory: 13266  loss: 2.2255  decode.loss_ce: 1.5297  decode.acc_seg: 79.8253  aux.loss_ce: 0.6958  aux.acc_seg: 78.3740
2025/07/31 07:55:59 - mmengine - INFO - Exp name: mamba_vision_160k_foodseg103-512x512_small_20250731_072419
2025/07/31 07:55:59 - mmengine - INFO - Iter(train) [  1000/160000]  base_lr: 3.3322e-05 lr: 3.3322e-05  eta: 3 days, 10:58:05  time: 1.8924  data_time: 0.0055  memory: 13266  loss: 2.7763  decode.loss_ce: 1.9539  decode.acc_seg: 42.9931  aux.loss_ce: 0.8224  aux.acc_seg: 29.5588
2025/07/31 07:57:32 - mmengine - INFO - Iter(train) [  1050/160000]  base_lr: 3.4990e-05 lr: 3.4990e-05  eta: 3 days, 10:55:04  time: 1.8805  data_time: 0.0049  memory: 13266  loss: 2.6088  decode.loss_ce: 1.8320  decode.acc_seg: 61.0786  aux.loss_ce: 0.7767  aux.acc_seg: 62.8475
2025/07/31 07:59:06 - mmengine - INFO - Iter(train) [  1100/160000]  base_lr: 3.6658e-05 lr: 3.6658e-05  eta: 3 days, 10:53:35  time: 1.9082  data_time: 0.0055  memory: 13266  loss: 2.3162  decode.loss_ce: 1.6184  decode.acc_seg: 68.4692  aux.loss_ce: 0.6978  aux.acc_seg: 69.3331
2025/07/31 08:00:40 - mmengine - INFO - Iter(train) [  1150/160000]  base_lr: 3.8326e-05 lr: 3.8326e-05  eta: 3 days, 10:51:22  time: 1.8751  data_time: 0.0052  memory: 13266  loss: 2.2535  decode.loss_ce: 1.5366  decode.acc_seg: 79.3997  aux.loss_ce: 0.7169  aux.acc_seg: 44.2057
2025/07/31 08:02:13 - mmengine - INFO - Iter(train) [  1200/160000]  base_lr: 3.9993e-05 lr: 3.9993e-05  eta: 3 days, 10:49:16  time: 1.8804  data_time: 0.0054  memory: 13266  loss: 2.4499  decode.loss_ce: 1.7213  decode.acc_seg: 56.5266  aux.loss_ce: 0.7286  aux.acc_seg: 52.4247
2025/07/31 08:03:47 - mmengine - INFO - Iter(train) [  1250/160000]  base_lr: 4.1661e-05 lr: 4.1661e-05  eta: 3 days, 10:47:40  time: 1.8820  data_time: 0.0052  memory: 13266  loss: 2.1279  decode.loss_ce: 1.5187  decode.acc_seg: 76.3749  aux.loss_ce: 0.6092  aux.acc_seg: 69.4297
2025/07/31 08:05:21 - mmengine - INFO - Iter(train) [  1300/160000]  base_lr: 4.3329e-05 lr: 4.3329e-05  eta: 3 days, 10:46:23  time: 1.8925  data_time: 0.0053  memory: 13266  loss: 2.6665  decode.loss_ce: 1.9009  decode.acc_seg: 28.4838  aux.loss_ce: 0.7656  aux.acc_seg: 27.5166
2025/07/31 08:06:55 - mmengine - INFO - Iter(train) [  1350/160000]  base_lr: 4.4997e-05 lr: 4.4997e-05  eta: 3 days, 10:43:54  time: 1.8752  data_time: 0.0051  memory: 13266  loss: 2.4197  decode.loss_ce: 1.7036  decode.acc_seg: 50.1244  aux.loss_ce: 0.7161  aux.acc_seg: 42.9190
2025/07/31 08:08:28 - mmengine - INFO - Iter(train) [  1400/160000]  base_lr: 4.6664e-05 lr: 4.6664e-05  eta: 3 days, 10:41:22  time: 1.8832  data_time: 0.0051  memory: 13266  loss: 2.1249  decode.loss_ce: 1.5081  decode.acc_seg: 21.8121  aux.loss_ce: 0.6167  aux.acc_seg: 19.8694
2025/07/31 08:10:02 - mmengine - INFO - Iter(train) [  1450/160000]  base_lr: 4.8332e-05 lr: 4.8332e-05  eta: 3 days, 10:39:25  time: 1.8823  data_time: 0.0050  memory: 13266  loss: 2.1167  decode.loss_ce: 1.5050  decode.acc_seg: 67.4835  aux.loss_ce: 0.6117  aux.acc_seg: 59.8653
2025/07/31 08:11:35 - mmengine - INFO - Iter(train) [  1500/160000]  base_lr: 5.0000e-05 lr: 5.0000e-05  eta: 3 days, 10:37:22  time: 1.8822  data_time: 0.0050  memory: 13266  loss: 2.1403  decode.loss_ce: 1.4957  decode.acc_seg: 67.4953  aux.loss_ce: 0.6447  aux.acc_seg: 66.2012
2025/07/31 08:13:09 - mmengine - INFO - Iter(train) [  1550/160000]  base_lr: 4.9985e-05 lr: 4.9985e-05  eta: 3 days, 10:35:19  time: 1.8773  data_time: 0.0054  memory: 13266  loss: 2.1202  decode.loss_ce: 1.5100  decode.acc_seg: 81.5770  aux.loss_ce: 0.6101  aux.acc_seg: 86.3444
2025/07/31 08:14:43 - mmengine - INFO - Iter(train) [  1600/160000]  base_lr: 4.9969e-05 lr: 4.9969e-05  eta: 3 days, 10:34:04  time: 1.8838  data_time: 0.0052  memory: 13266  loss: 2.2601  decode.loss_ce: 1.6090  decode.acc_seg: 57.0616  aux.loss_ce: 0.6511  aux.acc_seg: 68.8812
2025/07/31 08:16:16 - mmengine - INFO - Iter(train) [  1650/160000]  base_lr: 4.9953e-05 lr: 4.9953e-05  eta: 3 days, 10:31:51  time: 1.8769  data_time: 0.0052  memory: 13266  loss: 2.3696  decode.loss_ce: 1.6814  decode.acc_seg: 62.4199  aux.loss_ce: 0.6882  aux.acc_seg: 58.3525
2025/07/31 08:17:50 - mmengine - INFO - Iter(train) [  1700/160000]  base_lr: 4.9937e-05 lr: 4.9937e-05  eta: 3 days, 10:30:07  time: 1.8704  data_time: 0.0051  memory: 13266  loss: 2.7812  decode.loss_ce: 1.9937  decode.acc_seg: 62.8937  aux.loss_ce: 0.7875  aux.acc_seg: 59.8322
2025/07/31 08:19:24 - mmengine - INFO - Iter(train) [  1750/160000]  base_lr: 4.9921e-05 lr: 4.9921e-05  eta: 3 days, 10:28:34  time: 1.8634  data_time: 0.0056  memory: 13266  loss: 2.4936  decode.loss_ce: 1.7829  decode.acc_seg: 80.0903  aux.loss_ce: 0.7107  aux.acc_seg: 83.6599
2025/07/31 08:20:58 - mmengine - INFO - Iter(train) [  1800/160000]  base_lr: 4.9906e-05 lr: 4.9906e-05  eta: 3 days, 10:27:03  time: 1.8559  data_time: 0.0053  memory: 13266  loss: 2.4520  decode.loss_ce: 1.7471  decode.acc_seg: 12.9204  aux.loss_ce: 0.7049  aux.acc_seg: 10.1530
2025/07/31 08:22:31 - mmengine - INFO - Iter(train) [  1850/160000]  base_lr: 4.9890e-05 lr: 4.9890e-05  eta: 3 days, 10:24:57  time: 1.8636  data_time: 0.0057  memory: 13266  loss: 2.2257  decode.loss_ce: 1.5741  decode.acc_seg: 51.3986  aux.loss_ce: 0.6516  aux.acc_seg: 47.2823
2025/07/31 08:24:05 - mmengine - INFO - Iter(train) [  1900/160000]  base_lr: 4.9874e-05 lr: 4.9874e-05  eta: 3 days, 10:23:07  time: 1.8587  data_time: 0.0052  memory: 13266  loss: 2.4227  decode.loss_ce: 1.7388  decode.acc_seg: 40.5687  aux.loss_ce: 0.6839  aux.acc_seg: 36.4582
2025/07/31 08:25:38 - mmengine - INFO - Iter(train) [  1950/160000]  base_lr: 4.9858e-05 lr: 4.9858e-05  eta: 3 days, 10:21:01  time: 1.8605  data_time: 0.0051  memory: 13266  loss: 2.3657  decode.loss_ce: 1.6570  decode.acc_seg: 57.0505  aux.loss_ce: 0.7087  aux.acc_seg: 57.9350
2025/07/31 08:27:11 - mmengine - INFO - Exp name: mamba_vision_160k_foodseg103-512x512_small_20250731_072419
2025/07/31 08:27:11 - mmengine - INFO - Iter(train) [  2000/160000]  base_lr: 4.9843e-05 lr: 4.9843e-05  eta: 3 days, 10:19:00  time: 1.8647  data_time: 0.0052  memory: 13266  loss: 1.8359  decode.loss_ce: 1.3097  decode.acc_seg: 52.8872  aux.loss_ce: 0.5261  aux.acc_seg: 54.7901
2025/07/31 08:28:45 - mmengine - INFO - Iter(train) [  2050/160000]  base_lr: 4.9827e-05 lr: 4.9827e-05  eta: 3 days, 10:16:59  time: 1.8762  data_time: 0.0056  memory: 13266  loss: 2.0296  decode.loss_ce: 1.4524  decode.acc_seg: 68.2301  aux.loss_ce: 0.5772  aux.acc_seg: 67.2075
2025/07/31 08:30:19 - mmengine - INFO - Iter(train) [  2100/160000]  base_lr: 4.9811e-05 lr: 4.9811e-05  eta: 3 days, 10:15:29  time: 1.8828  data_time: 0.0053  memory: 13266  loss: 2.1879  decode.loss_ce: 1.5618  decode.acc_seg: 59.4596  aux.loss_ce: 0.6261  aux.acc_seg: 50.9795
2025/07/31 08:31:52 - mmengine - INFO - Iter(train) [  2150/160000]  base_lr: 4.9795e-05 lr: 4.9795e-05  eta: 3 days, 10:12:57  time: 1.8750  data_time: 0.0053  memory: 13266  loss: 2.0321  decode.loss_ce: 1.4569  decode.acc_seg: 60.0637  aux.loss_ce: 0.5752  aux.acc_seg: 59.7031
2025/07/31 08:33:25 - mmengine - INFO - Iter(train) [  2200/160000]  base_lr: 4.9779e-05 lr: 4.9779e-05  eta: 3 days, 10:11:15  time: 1.8725  data_time: 0.0056  memory: 13266  loss: 2.4562  decode.loss_ce: 1.7621  decode.acc_seg: 73.1285  aux.loss_ce: 0.6941  aux.acc_seg: 73.5751
2025/07/31 08:34:59 - mmengine - INFO - Iter(train) [  2250/160000]  base_lr: 4.9764e-05 lr: 4.9764e-05  eta: 3 days, 10:09:38  time: 1.8565  data_time: 0.0053  memory: 13266  loss: 2.1812  decode.loss_ce: 1.5899  decode.acc_seg: 49.1948  aux.loss_ce: 0.5913  aux.acc_seg: 49.0796
2025/07/31 08:36:32 - mmengine - INFO - Iter(train) [  2300/160000]  base_lr: 4.9748e-05 lr: 4.9748e-05  eta: 3 days, 10:07:38  time: 1.8553  data_time: 0.0054  memory: 13266  loss: 1.9791  decode.loss_ce: 1.4309  decode.acc_seg: 55.0560  aux.loss_ce: 0.5482  aux.acc_seg: 60.7241
2025/07/31 08:38:06 - mmengine - INFO - Iter(train) [  2350/160000]  base_lr: 4.9732e-05 lr: 4.9732e-05  eta: 3 days, 10:05:58  time: 1.8779  data_time: 0.0049  memory: 13266  loss: 2.2062  decode.loss_ce: 1.5748  decode.acc_seg: 54.8733  aux.loss_ce: 0.6315  aux.acc_seg: 58.9809
2025/07/31 08:39:40 - mmengine - INFO - Iter(train) [  2400/160000]  base_lr: 4.9716e-05 lr: 4.9716e-05  eta: 3 days, 10:04:28  time: 1.8617  data_time: 0.0053  memory: 13266  loss: 2.2082  decode.loss_ce: 1.5880  decode.acc_seg: 68.3922  aux.loss_ce: 0.6203  aux.acc_seg: 72.4117
2025/07/31 08:41:13 - mmengine - INFO - Iter(train) [  2450/160000]  base_lr: 4.9701e-05 lr: 4.9701e-05  eta: 3 days, 10:02:43  time: 1.8601  data_time: 0.0054  memory: 13266  loss: 2.2662  decode.loss_ce: 1.6057  decode.acc_seg: 53.5896  aux.loss_ce: 0.6605  aux.acc_seg: 52.1652
2025/07/31 08:42:32 - mmengine - INFO - Exp name: mamba_vision_160k_foodseg103-512x512_small_20250731_072419
2025/07/31 08:42:47 - mmengine - INFO - Iter(train) [  2500/160000]  base_lr: 4.9685e-05 lr: 4.9685e-05  eta: 3 days, 10:00:51  time: 1.8582  data_time: 0.0057  memory: 13266  loss: 1.6274  decode.loss_ce: 1.1807  decode.acc_seg: 70.2396  aux.loss_ce: 0.4468  aux.acc_seg: 81.1365
2025/07/31 08:44:20 - mmengine - INFO - Iter(train) [  2550/160000]  base_lr: 4.9669e-05 lr: 4.9669e-05  eta: 3 days, 9:59:01  time: 1.8662  data_time: 0.0053  memory: 13266  loss: 1.9901  decode.loss_ce: 1.4173  decode.acc_seg: 49.6789  aux.loss_ce: 0.5728  aux.acc_seg: 53.4869
2025/07/31 08:45:54 - mmengine - INFO - Iter(train) [  2600/160000]  base_lr: 4.9653e-05 lr: 4.9653e-05  eta: 3 days, 9:57:40  time: 1.8807  data_time: 0.0051  memory: 13266  loss: 1.9819  decode.loss_ce: 1.4025  decode.acc_seg: 55.6202  aux.loss_ce: 0.5794  aux.acc_seg: 59.3483
2025/07/31 08:47:28 - mmengine - INFO - Iter(train) [  2650/160000]  base_lr: 4.9638e-05 lr: 4.9638e-05  eta: 3 days, 9:56:15  time: 1.8823  data_time: 0.0048  memory: 13266  loss: 1.5857  decode.loss_ce: 1.1217  decode.acc_seg: 64.1593  aux.loss_ce: 0.4640  aux.acc_seg: 60.3792
2025/07/31 08:49:02 - mmengine - INFO - Iter(train) [  2700/160000]  base_lr: 4.9622e-05 lr: 4.9622e-05  eta: 3 days, 9:54:48  time: 1.8841  data_time: 0.0054  memory: 13266  loss: 2.2709  decode.loss_ce: 1.6249  decode.acc_seg: 47.6190  aux.loss_ce: 0.6461  aux.acc_seg: 62.3492
2025/07/31 08:50:36 - mmengine - INFO - Iter(train) [  2750/160000]  base_lr: 4.9606e-05 lr: 4.9606e-05  eta: 3 days, 9:53:25  time: 1.8891  data_time: 0.0050  memory: 13266  loss: 2.0255  decode.loss_ce: 1.4570  decode.acc_seg: 51.6919  aux.loss_ce: 0.5685  aux.acc_seg: 53.8167
2025/07/31 08:52:10 - mmengine - INFO - Iter(train) [  2800/160000]  base_lr: 4.9590e-05 lr: 4.9590e-05  eta: 3 days, 9:51:45  time: 1.8810  data_time: 0.0050  memory: 13266  loss: 2.2219  decode.loss_ce: 1.5790  decode.acc_seg: 50.4656  aux.loss_ce: 0.6430  aux.acc_seg: 53.2627
2025/07/31 08:53:43 - mmengine - INFO - Iter(train) [  2850/160000]  base_lr: 4.9574e-05 lr: 4.9574e-05  eta: 3 days, 9:50:24  time: 1.8940  data_time: 0.0055  memory: 13266  loss: 1.6704  decode.loss_ce: 1.1762  decode.acc_seg: 60.0353  aux.loss_ce: 0.4942  aux.acc_seg: 67.3779
2025/07/31 08:55:17 - mmengine - INFO - Iter(train) [  2900/160000]  base_lr: 4.9559e-05 lr: 4.9559e-05  eta: 3 days, 9:48:47  time: 1.8719  data_time: 0.0049  memory: 13266  loss: 2.0482  decode.loss_ce: 1.4513  decode.acc_seg: 71.0049  aux.loss_ce: 0.5969  aux.acc_seg: 68.3586
2025/07/31 08:56:51 - mmengine - INFO - Iter(train) [  2950/160000]  base_lr: 4.9543e-05 lr: 4.9543e-05  eta: 3 days, 9:47:21  time: 1.8877  data_time: 0.0051  memory: 13266  loss: 1.8943  decode.loss_ce: 1.3551  decode.acc_seg: 67.2832  aux.loss_ce: 0.5392  aux.acc_seg: 61.9781
2025/07/31 08:58:24 - mmengine - INFO - Exp name: mamba_vision_160k_foodseg103-512x512_small_20250731_072419
2025/07/31 08:58:24 - mmengine - INFO - Iter(train) [  3000/160000]  base_lr: 4.9527e-05 lr: 4.9527e-05  eta: 3 days, 9:45:25  time: 1.8769  data_time: 0.0051  memory: 13266  loss: 2.3036  decode.loss_ce: 1.6539  decode.acc_seg: 51.7010  aux.loss_ce: 0.6497  aux.acc_seg: 54.9614
2025/07/31 08:59:58 - mmengine - INFO - Iter(train) [  3050/160000]  base_lr: 4.9511e-05 lr: 4.9511e-05  eta: 3 days, 9:43:58  time: 1.8792  data_time: 0.0057  memory: 13266  loss: 1.8617  decode.loss_ce: 1.3634  decode.acc_seg: 64.2628  aux.loss_ce: 0.4983  aux.acc_seg: 77.4469
2025/07/31 09:01:32 - mmengine - INFO - Iter(train) [  3100/160000]  base_lr: 4.9496e-05 lr: 4.9496e-05  eta: 3 days, 9:42:30  time: 1.8786  data_time: 0.0059  memory: 13266  loss: 1.6628  decode.loss_ce: 1.1622  decode.acc_seg: 65.4136  aux.loss_ce: 0.5006  aux.acc_seg: 63.6050
2025/07/31 09:03:06 - mmengine - INFO - Iter(train) [  3150/160000]  base_lr: 4.9480e-05 lr: 4.9480e-05  eta: 3 days, 9:40:45  time: 1.8760  data_time: 0.0052  memory: 13266  loss: 1.8337  decode.loss_ce: 1.3026  decode.acc_seg: 48.1400  aux.loss_ce: 0.5311  aux.acc_seg: 41.3834
2025/07/31 09:04:39 - mmengine - INFO - Iter(train) [  3200/160000]  base_lr: 4.9464e-05 lr: 4.9464e-05  eta: 3 days, 9:39:05  time: 1.8869  data_time: 0.0054  memory: 13266  loss: 1.8753  decode.loss_ce: 1.3587  decode.acc_seg: 53.6818  aux.loss_ce: 0.5166  aux.acc_seg: 56.2841
2025/07/31 09:06:13 - mmengine - INFO - Iter(train) [  3250/160000]  base_lr: 4.9448e-05 lr: 4.9448e-05  eta: 3 days, 9:37:22  time: 1.8863  data_time: 0.0050  memory: 13266  loss: 1.9298  decode.loss_ce: 1.3858  decode.acc_seg: 32.2232  aux.loss_ce: 0.5440  aux.acc_seg: 33.4179
2025/07/31 09:07:46 - mmengine - INFO - Iter(train) [  3300/160000]  base_lr: 4.9432e-05 lr: 4.9432e-05  eta: 3 days, 9:35:32  time: 1.8816  data_time: 0.0050  memory: 13266  loss: 1.6255  decode.loss_ce: 1.1809  decode.acc_seg: 65.5394  aux.loss_ce: 0.4446  aux.acc_seg: 77.6728
2025/07/31 09:09:20 - mmengine - INFO - Iter(train) [  3350/160000]  base_lr: 4.9417e-05 lr: 4.9417e-05  eta: 3 days, 9:33:47  time: 1.8854  data_time: 0.0050  memory: 13266  loss: 1.7315  decode.loss_ce: 1.2411  decode.acc_seg: 50.4216  aux.loss_ce: 0.4903  aux.acc_seg: 50.7091
2025/07/31 09:10:53 - mmengine - INFO - Iter(train) [  3400/160000]  base_lr: 4.9401e-05 lr: 4.9401e-05  eta: 3 days, 9:32:13  time: 1.8847  data_time: 0.0052  memory: 13266  loss: 2.4907  decode.loss_ce: 1.8040  decode.acc_seg: 72.0701  aux.loss_ce: 0.6867  aux.acc_seg: 70.2986
2025/07/31 09:12:27 - mmengine - INFO - Iter(train) [  3450/160000]  base_lr: 4.9385e-05 lr: 4.9385e-05  eta: 3 days, 9:30:41  time: 1.8996  data_time: 0.0049  memory: 13266  loss: 2.8478  decode.loss_ce: 2.0542  decode.acc_seg: 39.3282  aux.loss_ce: 0.7936  aux.acc_seg: 38.8603
2025/07/31 09:14:00 - mmengine - INFO - Iter(train) [  3500/160000]  base_lr: 4.9369e-05 lr: 4.9369e-05  eta: 3 days, 9:28:48  time: 1.8871  data_time: 0.0048  memory: 13266  loss: 2.0168  decode.loss_ce: 1.4472  decode.acc_seg: 73.0527  aux.loss_ce: 0.5696  aux.acc_seg: 71.5610
2025/07/31 09:15:34 - mmengine - INFO - Iter(train) [  3550/160000]  base_lr: 4.9354e-05 lr: 4.9354e-05  eta: 3 days, 9:27:06  time: 1.8702  data_time: 0.0049  memory: 13266  loss: 1.7848  decode.loss_ce: 1.2505  decode.acc_seg: 63.3289  aux.loss_ce: 0.5343  aux.acc_seg: 54.1407
2025/07/31 09:17:08 - mmengine - INFO - Iter(train) [  3600/160000]  base_lr: 4.9338e-05 lr: 4.9338e-05  eta: 3 days, 9:25:31  time: 1.8802  data_time: 0.0058  memory: 13266  loss: 2.2917  decode.loss_ce: 1.6422  decode.acc_seg: 56.2968  aux.loss_ce: 0.6495  aux.acc_seg: 53.1415
2025/07/31 09:18:41 - mmengine - INFO - Iter(train) [  3650/160000]  base_lr: 4.9322e-05 lr: 4.9322e-05  eta: 3 days, 9:23:58  time: 1.8658  data_time: 0.0048  memory: 13266  loss: 2.0233  decode.loss_ce: 1.4357  decode.acc_seg: 65.9603  aux.loss_ce: 0.5876  aux.acc_seg: 60.7708
2025/07/31 09:20:15 - mmengine - INFO - Iter(train) [  3700/160000]  base_lr: 4.9306e-05 lr: 4.9306e-05  eta: 3 days, 9:22:26  time: 1.8621  data_time: 0.0063  memory: 13266  loss: 2.1254  decode.loss_ce: 1.5293  decode.acc_seg: 81.3740  aux.loss_ce: 0.5961  aux.acc_seg: 74.5724
2025/07/31 09:22:38 - mmengine - INFO - Iter(train) [  3750/160000]  base_lr: 4.9291e-05 lr: 4.9291e-05  eta: 3 days, 9:54:54  time: 3.0256  data_time: 0.0282  memory: 13266  loss: 1.7878  decode.loss_ce: 1.2948  decode.acc_seg: 78.0025  aux.loss_ce: 0.4930  aux.acc_seg: 76.9587
