#!/usr/bin/env python3
"""
简单的FoodSeg103推理脚本
"""

import os
import os.path as osp
import numpy as np
import matplotlib.pyplot as plt
import mmcv
from mmengine.config import Config
from mmseg.apis import init_model, inference_model
import argparse

# 导入MambaVision模型
import sys
sys.path.append('/22wangshenglong/FoodSeg/MambaVision/semantic_segmentation/tools')
import mamba_vision

# FoodSeg103类别名称（部分主要食物类别）
FOODSEG103_CLASSES = [
    'background', 'bread', 'egg', 'meat', 'rice', 'vegetable', 'fruit', 'soup',
    'noodle', 'fish', 'chicken', 'beef', 'pork', 'shrimp', 'crab', 'lobster',
    'cake', 'cookie', 'chocolate', 'ice_cream', 'pizza', 'hamburger', 'sandwich',
    'salad', 'pasta', 'cheese', 'milk', 'coffee', 'tea', 'juice', 'wine', 'beer'
    # ... 还有更多类别，总共104个
]

def parse_args():
    parser = argparse.ArgumentParser(description='FoodSeg103单张图片推理')
    parser.add_argument('config', help='配置文件路径')
    parser.add_argument('checkpoint', help='检查点文件路径')
    parser.add_argument('image', help='输入图像路径')
    parser.add_argument('--device', default='cuda:0', help='设备')
    parser.add_argument('--output', help='输出图像路径', default='result.png')
    args = parser.parse_args()
    return args

def main():
    args = parse_args()
    
    # 加载配置文件
    cfg = Config.fromfile(args.config)
    
    # 初始化模型
    print(f"正在加载模型: {args.checkpoint}")
    model = init_model(cfg, args.checkpoint, device=args.device)
    print("模型加载完成!")
    
    # 读取图像
    print(f"正在处理图像: {args.image}")
    img = mmcv.imread(args.image)
    
    # 推理
    print("正在进行推理...")
    result = inference_model(model, img)
    
    # 获取分割结果
    seg_map = result.pred_sem_seg.data[0].cpu().numpy()
    
    # 统计分割结果
    unique_classes = np.unique(seg_map)
    print(f"检测到的类别数量: {len(unique_classes)}")
    print(f"检测到的类别ID: {unique_classes}")
    
    # 计算每个类别的像素数量
    total_pixels = seg_map.size
    for class_id in unique_classes:
        pixel_count = np.sum(seg_map == class_id)
        percentage = (pixel_count / total_pixels) * 100
        class_name = FOODSEG103_CLASSES[class_id] if class_id < len(FOODSEG103_CLASSES) else f'class_{class_id}'
        print(f"类别 {class_id} ({class_name}): {pixel_count} 像素 ({percentage:.2f}%)")
    
    # 可视化
    plt.figure(figsize=(15, 5))
    
    # 原始图像
    plt.subplot(1, 3, 1)
    plt.imshow(mmcv.bgr2rgb(img))
    plt.title('Original Image')
    plt.axis('off')
    
    # 分割掩码
    plt.subplot(1, 3, 2)
    plt.imshow(seg_map, cmap='tab20')
    plt.title('Segmentation Mask')
    plt.axis('off')
    
    # 叠加结果
    plt.subplot(1, 3, 3)
    img_rgb = mmcv.bgr2rgb(img)
    
    # 创建彩色分割掩码
    colored_mask = np.zeros_like(img_rgb)
    colors = plt.cm.tab20(np.linspace(0, 1, 20))
    
    for i, class_id in enumerate(unique_classes):
        if class_id == 0:  # 背景设为透明
            continue
        color = colors[i % len(colors)][:3]  # 取RGB，忽略alpha
        colored_mask[seg_map == class_id] = (np.array(color) * 255).astype(np.uint8)
    
    # 叠加
    alpha = 0.6
    overlay = img_rgb.copy()
    mask_indices = np.any(colored_mask > 0, axis=2)
    overlay[mask_indices] = (alpha * colored_mask[mask_indices] + 
                            (1 - alpha) * img_rgb[mask_indices]).astype(np.uint8)
    
    plt.imshow(overlay)
    plt.title('Overlay Result')
    plt.axis('off')
    
    # 保存结果
    plt.tight_layout()
    plt.savefig(args.output, dpi=200, bbox_inches='tight')
    print(f"结果已保存到: {args.output}")
    
    plt.show()

if __name__ == '__main__':
    main()
