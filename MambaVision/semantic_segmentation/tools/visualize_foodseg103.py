#!/usr/bin/env python3
"""
可视化FoodSeg103测试集上的分割结果
"""

import os
import os.path as osp
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import mmcv
from mmengine.config import Config
from mmengine.registry import RUNNERS
from mmengine.runner import Runner
from mmseg.apis import init_model, inference_model
import cv2
import random
import argparse

# 导入MambaVision模型
import sys
sys.path.append('/22wangshenglong/FoodSeg/MambaVision/semantic_segmentation/tools')
import mamba_vision

def parse_args():
    parser = argparse.ArgumentParser(description='可视化FoodSeg103测试集上的分割结果')
    parser.add_argument('config', help='配置文件路径')
    parser.add_argument('checkpoint', help='检查点文件路径')
    parser.add_argument('--test-dir', help='测试图像目录', default='/22wangshenglong/FoodSeg/MambaVision/FoodSeg103/test_folder/img_dir/test')
    parser.add_argument('--num-images', type=int, default=5, help='要可视化的图像数量')
    parser.add_argument('--device', default='cuda:0', help='设备')
    parser.add_argument('--output-dir', default='work_dirs/foodseg103_vis', help='输出目录')
    args = parser.parse_args()
    return args

def main():
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载配置文件
    cfg = Config.fromfile(args.config)
    
    # 初始化模型
    model = init_model(cfg, args.checkpoint, device=args.device)
    
    # 获取测试图像列表
    image_files = []
    for root, _, files in os.walk(args.test_dir):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                image_files.append(osp.join(root, file))
    
    # 随机选择图像
    if len(image_files) > args.num_images:
        image_files = random.sample(image_files, args.num_images)
    
    # 为每个类别生成随机颜色
    num_classes = 104  # FoodSeg103有104个类别（包括背景）
    np.random.seed(42)
    colors = np.random.rand(num_classes, 3)
    # 设置背景为黑色
    colors[0] = [0, 0, 0]
    cmap = ListedColormap(colors)
    
    # 对每个图像进行推理和可视化
    for i, img_path in enumerate(image_files):
        # 读取图像
        img = mmcv.imread(img_path)
        
        # 推理
        result = inference_model(model, img)
        
        # 获取分割结果
        seg_map = result.pred_sem_seg.data[0].cpu().numpy()
        
        # 可视化
        plt.figure(figsize=(15, 5))
        
        # 原始图像
        plt.subplot(1, 3, 1)
        plt.imshow(mmcv.bgr2rgb(img))
        plt.title('原始图像')
        plt.axis('off')
        
        # 分割掩码
        plt.subplot(1, 3, 2)
        plt.imshow(seg_map, cmap=cmap, vmin=0, vmax=num_classes-1)
        plt.title('分割掩码')
        plt.axis('off')
        
        # 叠加结果
        plt.subplot(1, 3, 3)
        img_rgb = mmcv.bgr2rgb(img)
        seg_img = np.zeros_like(img_rgb)
        for c in range(1, num_classes):  # 跳过背景
            seg_img[seg_map == c] = (colors[c] * 255).astype(np.uint8)
        
        # 叠加原图和分割结果
        alpha = 0.5
        overlay = cv2.addWeighted(img_rgb, 1-alpha, seg_img, alpha, 0)
        plt.imshow(overlay)
        plt.title('叠加结果')
        plt.axis('off')
        
        # 保存图像
        output_path = osp.join(args.output_dir, f'vis_{i+1}.png')
        plt.tight_layout()
        plt.savefig(output_path, dpi=200)
        plt.close()
        
        print(f'已保存可视化结果到 {output_path}')

if __name__ == '__main__':
    main()
