import torch
from timm.models import create_model, load_checkpoint
import argparse

parser = argparse.ArgumentParser()
parser.add_argument('--model', '-m', metavar='NAME', default='mamba_vision_T', help='model architecture (default: mamba_vision_T)')
parser.add_argument('--checkpoint', default='', type=str, metavar='PATH',help='path to latest checkpoint (default: none)')
parser.add_argument('--use_pip', action='store_true', default=False, help='to use pip package')
args = parser.parse_args()

# Define mamba_vision_T model with 224 x 224 resolution

if args.use_pip:
      from mambavision import create_model
      model = create_model(args.model, pretrained=False)  # Don't download, we'll load manually
      if args.checkpoint:
        checkpoint = torch.load(args.checkpoint, map_location='cpu', weights_only=False)
        if 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        elif 'model' in checkpoint:
            state_dict = checkpoint['model']
        else:
            state_dict = checkpoint
        # Remove 'module.' prefix if present
        if list(state_dict.keys())[0].startswith('module.'):
            state_dict = {k[7:]: v for k, v in state_dict.items()}
        model.load_state_dict(state_dict, strict=False)
        print(f'Loaded checkpoint from {args.checkpoint}')
else:
      from models.mamba_vision import *
      model = create_model(args.model)
      if args.checkpoint:
        load_checkpoint(model, args.checkpoint, None)
        
print('{} model succesfully created !'.format(args.model))

image = torch.rand(1, 3, 754, 234).cuda() # place image on cuda

model = model.cuda() # place model on cuda

output = model(image) # output logit size is [1, 1000]

print('Inference succesfully completed on dummy input !')

