#!/usr/bin/env python3
"""
Simplified MambaVision registration without mamba_ssm dependencies
"""

import torch
import torch.nn as nn
from mmseg.registry import MODELS

# 创建一个简化的MambaVision backbone，使用标准的CNN结构作为替代
@MODELS.register_module()
class MM_mamba_vision(nn.Module):
    """
    Simplified MambaVision backbone using standard CNN layers
    This is a fallback implementation when mamba_ssm is not available
    """
    
    def __init__(self, 
                 out_indices=(0, 1, 2, 3),
                 pretrained=None,
                 depths=(1, 3, 8, 4),
                 num_heads=(2, 4, 8, 16),
                 window_size=(8, 8, 64, 32),
                 dim=80,
                 in_dim=32,
                 mlp_ratio=4,
                 drop_path_rate=0.3,
                 norm_layer="ln2d",
                 layer_scale=None,
                 **kwargs):
        super().__init__()
        
        self.out_indices = out_indices
        self.depths = depths
        self.num_heads = num_heads
        self.dim = dim
        
        # 输入投影
        self.patch_embed = nn.Conv2d(3, in_dim, kernel_size=4, stride=4)
        
        # 创建多个阶段
        self.stages = nn.ModuleList()
        in_channels = in_dim
        
        for i, (depth, num_head) in enumerate(zip(depths, num_heads)):
            out_channels = dim * (2 ** i) if i > 0 else dim
            
            stage = nn.Sequential()
            
            # 下采样层（除了第一个阶段）
            if i > 0:
                stage.add_module('downsample', 
                    nn.Conv2d(in_channels, out_channels, kernel_size=2, stride=2))
            else:
                stage.add_module('proj', 
                    nn.Conv2d(in_channels, out_channels, kernel_size=1))
            
            # 添加多个残差块
            for j in range(depth):
                block = nn.Sequential(
                    nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
                    nn.BatchNorm2d(out_channels),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
                    nn.BatchNorm2d(out_channels),
                )
                stage.add_module(f'block_{j}', block)
            
            self.stages.append(stage)
            in_channels = out_channels
        
        # 加载预训练权重（如果提供）
        if pretrained:
            self.load_pretrained(pretrained)
    
    def load_pretrained(self, pretrained_path):
        """加载预训练权重（简化版本）"""
        try:
            print(f"Loading pretrained weights from: {pretrained_path}")
            checkpoint = torch.load(pretrained_path, map_location='cpu', weights_only=False)
            
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint
            
            # 只加载兼容的权重
            model_dict = self.state_dict()
            compatible_dict = {}
            
            for k, v in state_dict.items():
                if k in model_dict and model_dict[k].shape == v.shape:
                    compatible_dict[k] = v
            
            if compatible_dict:
                self.load_state_dict(compatible_dict, strict=False)
                print(f"Loaded {len(compatible_dict)} compatible parameters")
            else:
                print("No compatible parameters found, using random initialization")
                
        except Exception as e:
            print(f"Failed to load pretrained weights: {e}")
            print("Using random initialization")
    
    def forward(self, x):
        """前向传播"""
        # Patch embedding
        x = self.patch_embed(x)  # [B, in_dim, H/4, W/4]
        
        features = []
        
        for i, stage in enumerate(self.stages):
            x = stage(x)
            
            if i in self.out_indices:
                features.append(x)
        
        return features

# 注册模型
print("✅ Simplified MambaVision registered successfully!")
print("⚠️  Note: This is a CNN-based fallback implementation")
print("   For full MambaVision functionality, mamba_ssm is required")
