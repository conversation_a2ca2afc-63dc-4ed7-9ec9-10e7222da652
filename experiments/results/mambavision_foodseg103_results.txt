===============================================================================
MambaVision on FoodSeg103 Dataset - Experimental Results
===============================================================================
Dataset: FoodSeg103 (104 classes including background)
Task: Semantic Segmentation
Input Size: 512x512
Training Iterations: 160,000
Validation Interval: 16,000

===============================================================================
1. MambaVision Tiny
===============================================================================
Model Parameters: ~31.79M
Pretrained Weights: mambavision_tiny_1k.pth.tar
Configuration: mamba_vision_160k_foodseg103-512x512_tiny.py

Architecture Details:
- depths: (1, 3, 8, 4)
- num_heads: (2, 4, 8, 16)  
- window_size: (8, 8, 64, 32)
- dim: 80
- in_dim: 32
- drop_path_rate: 0.3

Training Configuration:
- Optimizer: AdamW (lr=6e-05, weight_decay=0.01)
- Batch Size: 4
- Training Time: ~17 hours 25 minutes

Best Results (iter_144000.pth):
- aAcc: 85.87%
- mIoU: 39.88%
- mAcc: 65.95%

Final Results (iter_160000.pth):
- aAcc: 82.96%
- mIoU: 39.21%
- mAcc: 65.42%

Training Progress:
Iter 16K:  aAcc=79.14%, mIoU=26.49%, mAcc=51.83%
Iter 32K:  aAcc=76.67%, mIoU=34.03%, mAcc=64.92%
Iter 48K:  aAcc=80.83%, mIoU=34.33%, mAcc=59.06%
Iter 64K:  aAcc=79.10%, mIoU=37.62%, mAcc=65.88%
Iter 80K:  aAcc=83.99%, mIoU=31.04%, mAcc=65.61%
Iter 96K:  aAcc=78.11%, mIoU=33.67%, mAcc=64.42%
Iter 112K: aAcc=81.55%, mIoU=37.15%, mAcc=64.85%
Iter 128K: aAcc=83.98%, mIoU=38.62%, mAcc=68.07%
Iter 144K: aAcc=85.87%, mIoU=39.88%, mAcc=65.95% ← BEST
Iter 160K: aAcc=82.96%, mIoU=39.21%, mAcc=65.42%

Status: ✅ COMPLETED

===============================================================================
2. MambaVision Small
===============================================================================
Model Parameters: ~TBD
Pretrained Weights: mamba_vision_160k_ade20k-512x512_small.pth
Configuration: mamba_vision_160k_foodseg103-512x512_small.py

Architecture Details:
- depths: (3, 3, 7, 5)
- num_heads: (2, 4, 8, 16)
- window_size: (8, 8, 160, 56)
- dim: 96
- in_dim: 64
- drop_path_rate: 0.7

Training Configuration:
- Optimizer: AdamW (lr=5e-05, weight_decay=0.01)
- Batch Size: 2
- Training Time: TBD

Results: TBD

Status: 🔄 IN PROGRESS

===============================================================================
3. MambaVision Base
===============================================================================
Status: ⏳ PENDING

===============================================================================
4. MambaVision Large
===============================================================================
Status: ⏳ PENDING

===============================================================================
Summary
===============================================================================
Best Model So Far: MambaVision Tiny (39.88% mIoU)
